 Warning: Extra attributes from the server: webcrx 
overrideMethod @ installHook.js:1
 🎨 [TEMPLATE EDITOR] Initializing editor hook...
 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: false
 🎨 [TEMPLATE EDITOR] No canvas available, returning undefined editor
 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: false
 🎨 [TEMPLATE EDITOR] No canvas available, returning undefined editor
 🎨 [TEMPLATE EDITOR] Component rendering with: Object
 🎨 [TEMPLATE EDITOR] Initializing editor hook...
 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
 🎨 [TEMPLATE EDITOR] Component rendering with: Object
 Warning: Chrome: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. 
overrideMethod @ installHook.js:1
 Warning: Checkboard: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. 
overrideMethod @ installHook.js:1
 Warning: Circle: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. 
overrideMethod @ installHook.js:1
 Warning: CircleSwatch: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. 
overrideMethod @ installHook.js:1
 🎨 [TEMPLATE EDITOR] Component mounted with initial data: Object
 🎨 [TEMPLATE EDITOR] Initial JSON structure: Object
 🎨 [TEMPLATE EDITOR] Canvas state cleared in useEditor
 🎨 [TEMPLATE EDITOR] Container state cleared in useEditor
 🎨 [TEMPLATE EDITOR] Canvas initialization effect triggered
 🎨 [TEMPLATE EDITOR] Starting immediate canvas initialization
 🎨 [TEMPLATE EDITOR] Attempting canvas initialization...
 🎨 [TEMPLATE EDITOR] Container dimensions: Object
 🎨 [TEMPLATE EDITOR] Creating fabric canvas...
 🎨 [TEMPLATE EDITOR] Fabric canvas created, initializing editor...
 🎨 [TEMPLATE EDITOR] Init function called with: Object
 🎨 [TEMPLATE EDITOR] Fabric.js object prototypes configured
 🎨 [TEMPLATE EDITOR] Text baseline fixes applied
 🎨 [TEMPLATE EDITOR] Render method override applied
 🎨 [TEMPLATE EDITOR] Calculated dimensions: Object
 🎨 [TEMPLATE EDITOR] Creating initial workspace...
 🎨 [TEMPLATE EDITOR] Setting canvas dimensions...
 🎨 [TEMPLATE EDITOR] Adding workspace to canvas...
 🎨 [TEMPLATE EDITOR] Setting canvas and container state...
 🎨 [TEMPLATE EDITOR] Initializing canvas history...
 🎨 [TEMPLATE EDITOR] Init function completed successfully: Object
 🎨 [TEMPLATE EDITOR] Canvas initialization completed successfully
 🎨 [TEMPLATE EDITOR] Component unmounting
 🎨 [TEMPLATE EDITOR] Component mounted with initial data: Object
 🎨 [TEMPLATE EDITOR] Initial JSON structure: Object
 🎨 [TEMPLATE EDITOR] Canvas state cleared in useEditor
 🎨 [TEMPLATE EDITOR] Container state cleared in useEditor
 🎨 [TEMPLATE EDITOR] Canvas initialization effect triggered
 🎨 [TEMPLATE EDITOR] Starting immediate canvas initialization
 🎨 [TEMPLATE EDITOR] Attempting canvas initialization...
 🎨 [TEMPLATE EDITOR] Container dimensions: Object
 🎨 [TEMPLATE EDITOR] Creating fabric canvas...
 🎨 [TEMPLATE EDITOR] Fabric canvas created, initializing editor...
 🎨 [TEMPLATE EDITOR] Init function called with: Object
 🎨 [TEMPLATE EDITOR] Fabric.js object prototypes configured
 🎨 [TEMPLATE EDITOR] Text baseline fixes applied
 🎨 [TEMPLATE EDITOR] Render method override applied
 🎨 [TEMPLATE EDITOR] Calculated dimensions: Object
 🎨 [TEMPLATE EDITOR] Creating initial workspace...
 🎨 [TEMPLATE EDITOR] Setting canvas dimensions...
 🎨 [TEMPLATE EDITOR] Adding workspace to canvas...
 🎨 [TEMPLATE EDITOR] Setting canvas and container state...
 🎨 [TEMPLATE EDITOR] Initializing canvas history...
 🎨 [TEMPLATE EDITOR] Init function completed successfully: Object
 🎨 [TEMPLATE EDITOR] Canvas initialization completed successfully
 🎨 [TEMPLATE EDITOR] Initializing editor hook...
 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
 🎨 [TEMPLATE EDITOR] Building editor with canvas: Object
 🎨 [TEMPLATE EDITOR] Editor built successfully
 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
 🎨 [TEMPLATE EDITOR] Building editor with canvas: Object
 🎨 [TEMPLATE EDITOR] Editor built successfully
 🎨 [TEMPLATE EDITOR] Component rendering with: Object
 🎨 [TEMPLATE EDITOR] Initializing editor hook...
 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
 🎨 [TEMPLATE EDITOR] Component rendering with: Object
 🎨 [TEMPLATE EDITOR] Canvas state updated in useEditor: Object
 🎨 [TEMPLATE EDITOR] Container state updated in useEditor: Object
 🎨 [TEMPLATE EDITOR] useLoadState: Attempting to load initial state
 🎨 [TEMPLATE EDITOR] useLoadState: Parsed JSON data: Object
 🎨 [TEMPLATE EDITOR] useLoadState: Object details: Array(8)
 🎨 [TEMPLATE EDITOR] useLoadState: Canvas dimensions: Object
 🎨 [TEMPLATE EDITOR] useLoadState: Loading JSON into canvas...
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
 🎨 [TEMPLATE EDITOR] Editor initialized: Object
 🎨 [TEMPLATE EDITOR] Canvas dimensions: Object
 🎨 [TEMPLATE EDITOR] Canvas event listeners attached
 🎨 [TEMPLATE EDITOR] Editor changed, generating thumbnail
 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
 🎨 [TEMPLATE EDITOR] useLoadState: JSON loaded successfully: Object
 🎨 [TEMPLATE EDITOR] useLoadState: Calling autoZoom...
 🎨 [TEMPLATE EDITOR] autoZoom called
 🎨 [TEMPLATE EDITOR] autoZoom: Container dimensions: Object
 🎨 [TEMPLATE EDITOR] autoZoom: Canvas center: Object
 🎨 [TEMPLATE EDITOR] autoZoom: Workspace dimensions: Object
 🎨 [TEMPLATE EDITOR] autoZoom: Calculated zoom values: Object
 🎨 [TEMPLATE EDITOR] autoZoom: Workspace center: Point
 🎨 [TEMPLATE EDITOR] autoZoom completed successfully: Object
 🎨 [TEMPLATE EDITOR] ✅ PAGE FULLY LOADED AND READY FOR INTERACTION ✅
 🎨 [TEMPLATE EDITOR] useLoadState: Template loading complete: Object
 🎨 [TEMPLATE EDITOR] Initializing editor hook...
 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
 🎨 [TEMPLATE EDITOR] Component rendering with: Object
 🎨 [TEMPLATE EDITOR] Initializing editor hook...
 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
 🎨 [TEMPLATE EDITOR] Component rendering with: Object
 🎨 [TEMPLATE EDITOR] Generating initial thumbnail after 2s delay
 🎨 [TEMPLATE EDITOR] Debounced save triggered: Object
 🎨 [TEMPLATE EDITOR] Initializing editor hook...
 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
 🎨 [TEMPLATE EDITOR] Component rendering with: Object
 🎨 [TEMPLATE EDITOR] Initializing editor hook...
 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
 🎨 [TEMPLATE EDITOR] Component rendering with: Object
 🎨 [TEMPLATE EDITOR] Initializing editor hook...
 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
 🎨 [TEMPLATE EDITOR] Component rendering with: Object
 🎨 [TEMPLATE EDITOR] Initializing editor hook...
 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
use-editor.ts:880 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
use-editor.ts:883 🎨 [TEMPLATE EDITOR] Building editor with canvas: Object
use-editor.ts:915 🎨 [TEMPLATE EDITOR] Editor built successfully
hook.js:377 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
hook.js:377 🎨 [TEMPLATE EDITOR] Building editor with canvas: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Editor built successfully
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:197 🎨 [TEMPLATE EDITOR] Removing canvas event listeners
editor.tsx:153 🎨 [TEMPLATE EDITOR] Editor initialized: Object
editor.tsx:164 🎨 [TEMPLATE EDITOR] Canvas dimensions: Object
editor.tsx:188 🎨 [TEMPLATE EDITOR] Canvas event listeners attached
editor.tsx:209 🎨 [TEMPLATE EDITOR] Editor changed, generating thumbnail
editor.tsx:219 🎨 [TEMPLATE EDITOR] 🎯 COMPONENT FULLY MOUNTED AND INTERACTIVE 🎯: Object
editor.tsx:192 🎨 [TEMPLATE EDITOR] Generating initial thumbnail after 2s delay
hot-reloader-client.js:187 [Fast Refresh] rebuilding
hot-reloader-client.js:44 [Fast Refresh] done in 2888ms
page.tsx:64 HomePage render (updated): Object
hook.js:377 HomePage render (updated): Object
editor.tsx:79 🎨 [TEMPLATE EDITOR] Component unmounting
editor.tsx:197 🎨 [TEMPLATE EDITOR] Removing canvas event listeners
page.tsx:36 fetchPublicProjects called
page.tsx:39 API response status: 200 true
page.tsx:46 API response data: Object
page.tsx:64 HomePage render (updated): Object
hook.js:377 HomePage render (updated): Object
hook.js:608 No workspace found, using entire canvas
overrideMethod @ hook.js:608
hot-reloader-client.js:187 [Fast Refresh] rebuilding
hot-reloader-client.js:44 [Fast Refresh] done in 2087ms
project-card.tsx:60 ProjectCard render: Object
hook.js:377 ProjectCard render: Object
project-card.tsx:85 Thumbnail loaded successfully: /uploads/1753529580709-zemnkyw1t88.jpg...
project-card.tsx:60 ProjectCard render: Object
hook.js:377 ProjectCard render: Object
project-card.tsx:60 ProjectCard render: Object
hook.js:377 ProjectCard render: Object
project-card.tsx:60 ProjectCard render: Object
hook.js:377 ProjectCard render: Object
project-card.tsx:60 ProjectCard render: Object
hook.js:377 ProjectCard render: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
use-editor.ts:880 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: false
use-editor.ts:919 🎨 [TEMPLATE EDITOR] No canvas available, returning undefined editor
hook.js:377 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: false
hook.js:377 🎨 [TEMPLATE EDITOR] No canvas available, returning undefined editor
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:47 🎨 [TEMPLATE EDITOR] Component mounted with initial data: Object
use-editor.ts:810 🎨 [TEMPLATE EDITOR] Canvas state cleared in useEditor
use-editor.ts:823 🎨 [TEMPLATE EDITOR] Container state cleared in useEditor
editor.tsx:273 🎨 [TEMPLATE EDITOR] Canvas initialization effect triggered
editor.tsx:317 🎨 [TEMPLATE EDITOR] Starting immediate canvas initialization
editor.tsx:276 🎨 [TEMPLATE EDITOR] Attempting canvas initialization...
editor.tsx:288 🎨 [TEMPLATE EDITOR] Container dimensions: Object
editor.tsx:300 🎨 [TEMPLATE EDITOR] Creating fabric canvas...
editor.tsx:306 🎨 [TEMPLATE EDITOR] Fabric canvas created, initializing editor...
use-editor.ts:948 🎨 [TEMPLATE EDITOR] Init function called with: Object
use-editor.ts:966 🎨 [TEMPLATE EDITOR] Fabric.js object prototypes configured
use-editor.ts:979 🎨 [TEMPLATE EDITOR] Text baseline fixes applied
use-editor.ts:991 🎨 [TEMPLATE EDITOR] Render method override applied
use-editor.ts:999 🎨 [TEMPLATE EDITOR] Calculated dimensions: Object
use-editor.ts:1013 🎨 [TEMPLATE EDITOR] Creating initial workspace...
use-editor.ts:1027 🎨 [TEMPLATE EDITOR] Setting canvas dimensions...
use-editor.ts:1031 🎨 [TEMPLATE EDITOR] Adding workspace to canvas...
use-editor.ts:1036 🎨 [TEMPLATE EDITOR] Setting canvas and container state...
use-editor.ts:1040 🎨 [TEMPLATE EDITOR] Initializing canvas history...
use-editor.ts:1047 🎨 [TEMPLATE EDITOR] Init function completed successfully: Object
editor.tsx:312 🎨 [TEMPLATE EDITOR] Canvas initialization completed successfully
editor.tsx:79 🎨 [TEMPLATE EDITOR] Component unmounting
editor.tsx:47 🎨 [TEMPLATE EDITOR] Component mounted with initial data: Object
use-editor.ts:810 🎨 [TEMPLATE EDITOR] Canvas state cleared in useEditor
use-editor.ts:823 🎨 [TEMPLATE EDITOR] Container state cleared in useEditor
editor.tsx:273 🎨 [TEMPLATE EDITOR] Canvas initialization effect triggered
editor.tsx:317 🎨 [TEMPLATE EDITOR] Starting immediate canvas initialization
editor.tsx:276 🎨 [TEMPLATE EDITOR] Attempting canvas initialization...
editor.tsx:288 🎨 [TEMPLATE EDITOR] Container dimensions: Object
editor.tsx:300 🎨 [TEMPLATE EDITOR] Creating fabric canvas...
editor.tsx:306 🎨 [TEMPLATE EDITOR] Fabric canvas created, initializing editor...
use-editor.ts:948 🎨 [TEMPLATE EDITOR] Init function called with: Object
use-editor.ts:966 🎨 [TEMPLATE EDITOR] Fabric.js object prototypes configured
use-editor.ts:979 🎨 [TEMPLATE EDITOR] Text baseline fixes applied
use-editor.ts:991 🎨 [TEMPLATE EDITOR] Render method override applied
use-editor.ts:999 🎨 [TEMPLATE EDITOR] Calculated dimensions: Object
use-editor.ts:1013 🎨 [TEMPLATE EDITOR] Creating initial workspace...
use-editor.ts:1027 🎨 [TEMPLATE EDITOR] Setting canvas dimensions...
use-editor.ts:1031 🎨 [TEMPLATE EDITOR] Adding workspace to canvas...
use-editor.ts:1036 🎨 [TEMPLATE EDITOR] Setting canvas and container state...
use-editor.ts:1040 🎨 [TEMPLATE EDITOR] Initializing canvas history...
use-editor.ts:1047 🎨 [TEMPLATE EDITOR] Init function completed successfully: Object
editor.tsx:312 🎨 [TEMPLATE EDITOR] Canvas initialization completed successfully
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
use-editor.ts:880 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
use-editor.ts:883 🎨 [TEMPLATE EDITOR] Building editor with canvas: Object
use-editor.ts:915 🎨 [TEMPLATE EDITOR] Editor built successfully
hook.js:377 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
hook.js:377 🎨 [TEMPLATE EDITOR] Building editor with canvas: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Editor built successfully
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
use-editor.ts:802 🎨 [TEMPLATE EDITOR] Canvas state updated in useEditor: Object
use-editor.ts:817 🎨 [TEMPLATE EDITOR] Container state updated in useEditor: Object
editor.tsx:153 🎨 [TEMPLATE EDITOR] Editor initialized: Object
editor.tsx:164 🎨 [TEMPLATE EDITOR] Canvas dimensions: Object
editor.tsx:188 🎨 [TEMPLATE EDITOR] Canvas event listeners attached
editor.tsx:209 🎨 [TEMPLATE EDITOR] Editor changed, generating thumbnail
editor.tsx:192 🎨 [TEMPLATE EDITOR] Generating initial thumbnail after 2s delay
editor.tsx:235 🎨 [TEMPLATE EDITOR] Active tool changing: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:235 🎨 [TEMPLATE EDITOR] Active tool changing: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:235 🎨 [TEMPLATE EDITOR] Active tool changing: Object
editor.tsx:252 🎨 [TEMPLATE EDITOR] Enabling pan mode
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:235 🎨 [TEMPLATE EDITOR] Active tool changing: Object
editor.tsx:257 🎨 [TEMPLATE EDITOR] Disabling pan mode
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:235 🎨 [TEMPLATE EDITOR] Active tool changing: Object
editor.tsx:252 🎨 [TEMPLATE EDITOR] Enabling pan mode
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:235 🎨 [TEMPLATE EDITOR] Active tool changing: Object
editor.tsx:257 🎨 [TEMPLATE EDITOR] Disabling pan mode
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
use-editor.ts:880 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
use-editor.ts:883 🎨 [TEMPLATE EDITOR] Building editor with canvas: Object
use-editor.ts:915 🎨 [TEMPLATE EDITOR] Editor built successfully
hook.js:377 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
hook.js:377 🎨 [TEMPLATE EDITOR] Building editor with canvas: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Editor built successfully
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:197 🎨 [TEMPLATE EDITOR] Removing canvas event listeners
editor.tsx:153 🎨 [TEMPLATE EDITOR] Editor initialized: Object
editor.tsx:164 🎨 [TEMPLATE EDITOR] Canvas dimensions: Object
editor.tsx:188 🎨 [TEMPLATE EDITOR] Canvas event listeners attached
editor.tsx:209 🎨 [TEMPLATE EDITOR] Editor changed, generating thumbnail
editor.tsx:219 🎨 [TEMPLATE EDITOR] 🎯 COMPONENT FULLY MOUNTED AND INTERACTIVE 🎯: Object
editor.tsx:192 🎨 [TEMPLATE EDITOR] Generating initial thumbnail after 2s delay
editor.tsx:91 🎨 [TEMPLATE EDITOR] Debounced save triggered: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
use-editor.ts:880 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
use-editor.ts:883 🎨 [TEMPLATE EDITOR] Building editor with canvas: Object
use-editor.ts:915 🎨 [TEMPLATE EDITOR] Editor built successfully
hook.js:377 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
hook.js:377 🎨 [TEMPLATE EDITOR] Building editor with canvas: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Editor built successfully
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:197 🎨 [TEMPLATE EDITOR] Removing canvas event listeners
editor.tsx:153 🎨 [TEMPLATE EDITOR] Editor initialized: Object
editor.tsx:164 🎨 [TEMPLATE EDITOR] Canvas dimensions: Object
editor.tsx:188 🎨 [TEMPLATE EDITOR] Canvas event listeners attached
editor.tsx:209 🎨 [TEMPLATE EDITOR] Editor changed, generating thumbnail
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:219 🎨 [TEMPLATE EDITOR] 🎯 COMPONENT FULLY MOUNTED AND INTERACTIVE 🎯: Object
editor.tsx:192 🎨 [TEMPLATE EDITOR] Generating initial thumbnail after 2s delay
editor.tsx:235 🎨 [TEMPLATE EDITOR] Active tool changing: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:219 🎨 [TEMPLATE EDITOR] 🎯 COMPONENT FULLY MOUNTED AND INTERACTIVE 🎯: Object
editor.tsx:235 🎨 [TEMPLATE EDITOR] Active tool changing: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:219 🎨 [TEMPLATE EDITOR] 🎯 COMPONENT FULLY MOUNTED AND INTERACTIVE 🎯: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
use-editor.ts:880 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
use-editor.ts:883 🎨 [TEMPLATE EDITOR] Building editor with canvas: Object
use-editor.ts:915 🎨 [TEMPLATE EDITOR] Editor built successfully
hook.js:377 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
hook.js:377 🎨 [TEMPLATE EDITOR] Building editor with canvas: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Editor built successfully
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:197 🎨 [TEMPLATE EDITOR] Removing canvas event listeners
editor.tsx:153 🎨 [TEMPLATE EDITOR] Editor initialized: Object
editor.tsx:164 🎨 [TEMPLATE EDITOR] Canvas dimensions: Object
editor.tsx:188 🎨 [TEMPLATE EDITOR] Canvas event listeners attached
editor.tsx:209 🎨 [TEMPLATE EDITOR] Editor changed, generating thumbnail
editor.tsx:219 🎨 [TEMPLATE EDITOR] 🎯 COMPONENT FULLY MOUNTED AND INTERACTIVE 🎯: Object
editor.tsx:192 🎨 [TEMPLATE EDITOR] Generating initial thumbnail after 2s delay
editor.tsx:91 🎨 [TEMPLATE EDITOR] Debounced save triggered: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:783 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
use-editor.ts:880 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
use-editor.ts:883 🎨 [TEMPLATE EDITOR] Building editor with canvas: Object
use-editor.ts:915 🎨 [TEMPLATE EDITOR] Editor built successfully
hook.js:377 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
hook.js:377 🎨 [TEMPLATE EDITOR] Building editor with canvas: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Editor built successfully
editor.tsx:340 🎨 [TEMPLATE EDITOR] Component rendering with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Initializing editor hook...
hook.js:377 🎨 [TEMPLATE EDITOR] useEditor hook called with: Object
hook.js:377 🎨 [TEMPLATE EDITOR] Component rendering with: Object
editor.tsx:197 🎨 [TEMPLATE EDITOR] Removing canvas event listeners
editor.tsx:153 🎨 [TEMPLATE EDITOR] Editor initialized: Object
editor.tsx:164 🎨 [TEMPLATE EDITOR] Canvas dimensions: Object
editor.tsx:188 🎨 [TEMPLATE EDITOR] Canvas event listeners attached
editor.tsx:209 🎨 [TEMPLATE EDITOR] Editor changed, generating thumbnail
editor.tsx:219 🎨 [TEMPLATE EDITOR] 🎯 COMPONENT FULLY MOUNTED AND INTERACTIVE 🎯: Object
editor.tsx:192 🎨 [TEMPLATE EDITOR] Generating initial thumbnail after 2s delay
hot-reloader-client.js:187 [Fast Refresh] rebuilding
hot-reloader-client.js:44 [Fast Refresh] done in 91ms
